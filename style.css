*, *:before, *:after {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

:root {
    --primary-gradient: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
    --secondary-gradient: linear-gradient(135deg, #16213e 0%, #0f3460 100%);
    --accent-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --shadow-primary: 0 8px 32px rgba(0, 0, 0, 0.6);
    --shadow-secondary: 0 4px 16px rgba(0, 0, 0, 0.4);
    --border-radius: 20px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    display: grid;
    place-items: center;
    font-family: 'Inter', '<PERSON><PERSON><PERSON>', <PERSON><PERSON><PERSON>, Geneva, Verdana, sans-serif;
    min-height: 100vh;
    background: var(--primary-gradient);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    position: relative;
    overflow-x: hidden;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(79, 172, 254, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

main {
    width: min(90%, 60rem);
    margin: 2rem;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-primary);
    padding: 2rem;
    position: relative;
    overflow: hidden;
    animation: mainPulse 4s ease-in-out infinite;
}

@keyframes mainPulse {
    0%, 100% {
        box-shadow: var(--shadow-primary);
    }
    50% {
        box-shadow:
            0 8px 32px rgba(31, 38, 135, 0.5),
            0 0 50px rgba(255, 255, 255, 0.1);
    }
}

main::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
}

.svg-container {
    margin: 2rem auto;
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    padding: 2rem;
    box-shadow: var(--shadow-secondary);
    position: relative;
    overflow: hidden;
    transition: var(--transition);
}

.svg-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: rotate 20s linear infinite;
    pointer-events: none;
}



@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@media screen and (min-width: 31.25rem) {
    .svg-container > * {
        width: 30%;
        height: auto;
    }
}

@media screen and (max-width: 31.25rem) {
    .svg-container > * {
        width: 50%;
        height: auto;
    }
}

.control-container {
    margin: 2rem auto;
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 2rem;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1.5rem;
    box-shadow: var(--shadow-secondary);
    position: relative;
}

.control-container button {
    appearance: none;
    border: none;
    padding: 1rem 2rem;
    border-radius: 15px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    color: var(--text-primary);
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    min-width: 120px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.control-container button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition);
}

.control-container button:hover::before {
    left: 100%;
}

.control-container button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.control-container button:active {
    transform: translateY(-1px);
}

.control-container button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.control-container button span {
    padding-left: .5rem;
    position: relative;
    z-index: 1;
}

/* Start Button */
.control-container button:nth-child(1) {
    background: linear-gradient(135deg, #00c851 0%, #007e33 100%);
    box-shadow: 0 4px 15px rgba(0, 200, 81, 0.4);
}

.control-container button:nth-child(1):hover {
    background: linear-gradient(135deg, #00a844 0%, #005a24 100%);
    box-shadow: 0 6px 20px rgba(0, 200, 81, 0.6);
}

/* Pause Button */
.control-container button:nth-child(2) {
    background: linear-gradient(135deg, #ffbb33 0%, #ff8800 100%);
    box-shadow: 0 4px 15px rgba(255, 187, 51, 0.4);
}

.control-container button:nth-child(2):hover {
    background: linear-gradient(135deg, #e6a429 0%, #cc7700 100%);
    box-shadow: 0 6px 20px rgba(255, 187, 51, 0.6);
}

/* Reset Button */
.control-container button:nth-child(3) {
    background: linear-gradient(135deg, #33b5e5 0%, #0099cc 100%);
    box-shadow: 0 4px 15px rgba(51, 181, 229, 0.4);
}

.control-container button:nth-child(3):hover {
    background: linear-gradient(135deg, #29a3d1 0%, #007399 100%);
    box-shadow: 0 6px 20px rgba(51, 181, 229, 0.6);
}

/* Lap Button */
.control-container button:nth-child(4) {
    background: linear-gradient(135deg, #aa66cc 0%, #9933cc 100%);
    box-shadow: 0 4px 15px rgba(170, 102, 204, 0.4);
}

.control-container button:nth-child(4):hover {
    background: linear-gradient(135deg, #9955b8 0%, #7a29a3 100%);
    box-shadow: 0 6px 20px rgba(170, 102, 204, 0.6);
}

/* Delete Button */
.control-container button:last-child {
    background: linear-gradient(135deg, #ff4444 0%, #cc0000 100%);
    box-shadow: 0 4px 15px rgba(255, 68, 68, 0.4);
}

.control-container button:last-child:hover {
    background: linear-gradient(135deg, #e63939 0%, #990000 100%);
    box-shadow: 0 6px 20px rgba(255, 68, 68, 0.6);
}

h1, h2 {
    margin: 2rem 1rem;
    color: var(--text-primary);
    text-align: center;
    font-family: 'Orbitron', 'Inter', sans-serif;
    font-weight: 700;
    font-size: 2.5rem;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

h1::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: var(--accent-gradient);
    border-radius: 2px;
}

/* Lap Section Styling */
.lap-section {
    margin: 2rem auto;
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-secondary);
    overflow: hidden;
    min-height: 200px;
}

.lap-header {
    padding: 1.5rem 2rem;
    background: var(--secondary-gradient);
    border-bottom: 1px solid var(--glass-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.lap-header h2 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.lap-header h2 i {
    color: var(--accent-gradient);
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.lap-stats {
    display: flex;
    gap: 2rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
    min-width: 200px;
    flex-shrink: 0;
}

.lap-stats span {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    font-weight: 500;
}

.lap-count {
    color: #4facfe;
}

.best-lap {
    color: #00c851;
}

.lap-container {
    position: relative;
    min-height: 150px;
}

.lap-list {
    max-height: 300px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.lap-list::-webkit-scrollbar {
    width: 8px;
}

.lap-list::-webkit-scrollbar-track {
    background: transparent;
}

.lap-list::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

.lap-list::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

.lap {
    padding: 1.5rem 2rem;
    background: transparent;
    display: flex;
    align-items: center;
    color: var(--text-primary);
    transition: var(--transition);
    border-bottom: 1px solid var(--glass-border);
    position: relative;
    gap: 1rem;
}

.lap::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
    height: 100%;
    background: var(--accent-gradient);
    transition: width 0.3s ease;
    opacity: 0.1;
}

.lap:hover::before {
    width: 4px;
}

.lap:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: translateX(5px);
}

.lap:last-child {
    border-bottom: none;
}

.lap-list li {
    list-style: none;
    font-size: 1rem;
    font-weight: 500;
}

.lap-serial {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: 0.9rem;
    color: var(--text-secondary);
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    padding: 0.3rem 0.6rem;
    min-width: 40px;
    text-align: center;
    flex-shrink: 0;
}

.lap-info {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
    flex: 1;
    min-width: 0;
    overflow: hidden;
    margin-left: 1rem;
}

.lap-number {
    font-weight: 700;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.lap-time {
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    color: #4facfe;
    font-size: 1.2rem;
    text-shadow: 0 0 10px rgba(79, 172, 254, 0.3);
}

.lap-diff {
    font-size: 0.8rem;
    font-weight: 500;
}

.lap-diff.faster {
    color: #00c851;
}

.lap-diff.slower {
    color: #ff6b6b;
}

.lap-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

/* Custom Checkbox Styling */
.lap-checkbox {
    position: relative;
    display: inline-block;
    width: 20px;
    height: 20px;
    flex-shrink: 0;
}

.lap-checkbox input[type="checkbox"] {
    opacity: 0;
    width: 100%;
    height: 100%;
    position: absolute;
    cursor: pointer;
}

.lap-checkbox .checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
    background: var(--glass-bg);
    border: 2px solid var(--glass-border);
    border-radius: 4px;
    transition: var(--transition);
}

.lap-checkbox:hover .checkmark {
    border-color: #4facfe;
    background: rgba(79, 172, 254, 0.1);
}

.lap-checkbox input:checked ~ .checkmark {
    background: var(--accent-gradient);
    border-color: #4facfe;
}

.lap-checkbox .checkmark:after {
    content: "";
    position: absolute;
    display: none;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.lap-checkbox input:checked ~ .checkmark:after {
    display: block;
}

.lap.selected {
    background: rgba(79, 172, 254, 0.1);
    border-left: 3px solid #4facfe;
}

.lap-list button {
    border: none;
    padding: 0.5rem;
    font-size: 0.9rem;
    color: #ff6b6b;
    background: rgba(255, 107, 107, 0.1);
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
}

.lap-list button:hover {
    background: rgba(255, 107, 107, 0.2);
    color: #ff5252;
    transform: scale(1.1);
}

.lap-list button:active {
    transform: scale(0.95);
}

/* Empty State */
.lap-empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 2rem;
    text-align: center;
    color: var(--text-secondary);
    min-height: 150px;
}

.lap-empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.lap-empty-state p {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.lap-empty-state small {
    font-size: 0.9rem;
    opacity: 0.7;
    max-width: 300px;
    line-height: 1.4;
}

.lap-empty-state.hidden {
    display: none;
}

/* Help Button */
.help-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    background: transparent;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    z-index: 1000;
    border: 2px solid var(--glass-border);
    backdrop-filter: blur(10px);
}

.help-button:hover {
    transform: scale(1.1);
    background: rgba(102, 126, 234, 0.1);
    border-color: #667eea;
    box-shadow:
        0 0 20px rgba(102, 126, 234, 0.4),
        0 0 40px rgba(102, 126, 234, 0.2),
        inset 0 0 20px rgba(102, 126, 234, 0.1);
}

.help-button::before {
    content: '?';
    color: var(--text-primary);
    font-size: 1.4rem;
    font-weight: 700;
    font-family: 'Inter', sans-serif;
}

.help-button:hover::before {
    color: #667eea;
    text-shadow: 0 0 10px rgba(102, 126, 234, 0.6);
}

/* Help Modal */
.help-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.help-modal.show {
    opacity: 1;
    visibility: visible;
}

.help-content {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-primary);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    transform: scale(0.8);
    transition: var(--transition);
}

.help-modal.show .help-content {
    transform: scale(1);
}

.help-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--glass-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--secondary-gradient);
}

.help-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.help-close {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: var(--transition);
}

.help-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.help-body {
    padding: 2rem;
}

.shortcut-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid var(--glass-border);
}

.shortcut-item:last-child {
    border-bottom: none;
}

.shortcut-key {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.shortcut-key kbd {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 6px;
    padding: 0.3rem 0.6rem;
    font-family: 'Orbitron', monospace;
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--text-primary);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    min-width: 30px;
    text-align: center;
}

.shortcut-desc {
    color: var(--text-secondary);
    font-weight: 500;
}

.help-footer {
    padding: 1.5rem 2rem;
    border-top: 1px solid var(--glass-border);
    background: var(--glass-bg);
    text-align: center;
}

.help-footer p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.help-footer i {
    color: #ffbb33;
}

hr {
    border: none;
    height: 1px;
    background: var(--glass-border);
    margin: 2rem 0;
}

/* Digital Clock Styling */
#time-minutes, #time-seconds, #time-milliseconds {
    font-family: 'Orbitron', monospace;
    font-weight: 900;
    text-shadow:
        0 0 10px currentColor,
        0 0 20px currentColor,
        0 0 30px currentColor;
    transition: color 0.3s ease;
}



/* Clock Ring Enhancements */
.clock-minutes path[id^="ring-"],
.clock-seconds path[id^="ring-"] {
    transition: all 0.5s ease;
}



/* Floating Particles Animation */
.floating-particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    animation: float 15s infinite linear;
}

@keyframes float {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100vh) rotate(360deg);
        opacity: 0;
    }
}

/* Enhanced SVG Styling */
.svg-container svg {
    transition: var(--transition);
}

/* Progress Ring Animation */
.clock-minutes path[id^="ring-"],
.clock-seconds path[id^="ring-"] {
    transition: opacity 0.3s ease, fill 0.3s ease;
}

/* Notification Styles */
.notification {
    font-family: 'Inter', sans-serif;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--text-primary);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    main {
        width: 95%;
        padding: 1rem;
        margin: 1rem;
    }

    h1 {
        font-size: 2rem;
    }

    .control-container {
        gap: 1rem;
        padding: 1.5rem;
    }

    .control-container button {
        min-width: 100px;
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }

    .svg-container {
        padding: 1rem;
    }

    .lap-header {
        padding: 1rem 1.5rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .lap-header h2 {
        font-size: 1.3rem;
    }

    .lap-stats {
        gap: 1rem;
        font-size: 0.8rem;
        min-width: auto;
        flex-direction: row;
        justify-content: flex-start;
    }

    .lap {
        padding: 1rem 1.5rem;
        justify-content: space-between;
        align-items: center;
    }

    .lap-info {
        flex: 1;
        min-width: 0;
    }
}

@media (max-width: 480px) {
    .control-container {
        flex-direction: column;
        align-items: center;
    }

    .control-container button {
        width: 100%;
        max-width: 200px;
    }

    .svg-container > * {
        width: 80% !important;
    }

    .lap-header {
        padding: 1rem;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .lap-header h2 {
        font-size: 1.2rem;
    }

    .lap-stats {
        flex-direction: column;
        gap: 0.5rem;
        align-items: center;
        min-width: auto;
    }

    .lap {
        padding: 1rem;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
    }

    .lap-info {
        flex: 1;
        min-width: 0;
    }

    .lap-time {
        font-size: 1rem !important;
    }

    .lap-checkbox {
        flex-shrink: 0;
    }

    .help-button {
        bottom: 15px;
        right: 15px;
        width: 45px;
        height: 45px;
    }

    .help-button::before {
        font-size: 1.2rem;
    }

    .help-content {
        width: 95%;
        margin: 1rem;
    }

    .help-header {
        padding: 1rem 1.5rem;
    }

    .help-header h3 {
        font-size: 1.1rem;
    }

    .help-body {
        padding: 1.5rem;
    }

    .shortcut-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
        padding: 0.8rem 0;
    }

    .shortcut-key {
        order: 2;
    }

    .shortcut-desc {
        order: 1;
        font-weight: 600;
        color: var(--text-primary);
    }

    .help-footer {
        padding: 1rem 1.5rem;
    }

    .help-footer p {
        font-size: 0.8rem;
        flex-direction: column;
        gap: 0.3rem;
    }
}
