// Get DOM elements
const minuteLabel = document.getElementById('time-minutes');
const secondLabel = document.getElementById('time-seconds');
const millisecondLabel = document.getElementById('time-milliseconds');
const startButton = document.querySelector('#btn-start');
const pauseButton = document.querySelector('#btn-pause');
const resetButton = document.querySelector('#btn-reset');
const lapButton = document.querySelector('#btn-lap');
const deleteButton = document.querySelector('#btn-delete');
const lapList = document.querySelector('.lap-list');

// Stopwatch variables
let minutes = 0;
let seconds = 0;
let milliseconds = 0;
let interval;

// Sound effects (using Web Audio API for better performance)
const audioContext = new (window.AudioContext || window.webkitAudioContext)();

function playClickSound() {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);

    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.1);
}

function playStartSound() {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(800, audioContext.currentTime + 0.2);

    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.2);
}

function playStopSound() {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(200, audioContext.currentTime + 0.3);

    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.3);
}

// Disable all buttons except the Start button initially
toggleButtonState(true);

// Event listeners for the buttons

// Start button click event
startButton.addEventListener('click', () => {
    playStartSound();
    addButtonAnimation(startButton);
    interval = setInterval(updateTimer, 10);
    startButton.disabled = true;
    pauseButton.disabled = false;
    lapButton.disabled = false;
    toggleButtonState(false);
});

// Pause button click event
pauseButton.addEventListener('click', () => {
    playStopSound();
    addButtonAnimation(pauseButton);
    clearInterval(interval);
    pauseButton.disabled = true;
    startButton.disabled = false;
    lapButton.disabled = true;
});

// Reset button click event
resetButton.addEventListener('click', () => {
    playClickSound();
    addButtonAnimation(resetButton);
    clearInterval(interval);
    resetTimeData();
    displayTimer();
    pauseButton.disabled = true;
    startButton.disabled = false;
    lapButton.disabled = true;

    // Add reset animation to the clock
    const svgContainer = document.querySelector('.svg-container');
    svgContainer.style.transform = 'scale(0.95)';
    setTimeout(() => {
        svgContainer.style.transform = 'scale(1)';
    }, 150);
});

// Lap button click event
lapButton.addEventListener('click', () => {
    playClickSound();
    addButtonAnimation(lapButton);
    addToList();
    deleteButton.disabled = false;
});

// Delete button click event
deleteButton.addEventListener('click', () => {
    playClickSound();
    addButtonAnimation(deleteButton);
    // Reset lap list to initial state
    lapList.innerHTML = '';
    deleteButton.disabled = true;
});

// Lap list click event to handle lap deletion
lapList.addEventListener('click', function (event) {
    // Check if the clicked element is a delete button
    if (event.target.tagName === 'I' && event.target.classList.contains('fa-trash')) {
        // Remove the corresponding lap when the delete button is clicked
        const lapItem = event.target.closest('.lap');
        if (lapItem) {
            lapItem.parentNode.removeChild(lapItem);

            // Disable the delete button if there are no laps remaining
            deleteButton.disabled = lapList.childElementCount === 0;
        }
    }
});

// Functions to handle the events

// Function to add button animation
function addButtonAnimation(button) {
    button.style.transform = 'scale(0.95)';
    setTimeout(() => {
        button.style.transform = 'scale(1)';
    }, 100);
}

// Function to toggle button states
function toggleButtonState(state) {
    pauseButton.disabled = state;
    resetButton.disabled = state;
    lapButton.disabled = state;
    deleteButton.disabled = state;
}

// Function to update the timer
function updateTimer() {
    milliseconds++;
    if (milliseconds === 100) {
        milliseconds = 0;
        seconds++;
        // Add subtle pulse animation every second
        if (seconds % 5 === 0) {
            addTimerPulse();
        }
    }
    if (seconds === 60) {
        seconds = 0;
        minutes++;
        // Add celebration effect every minute
        addCelebrationEffect();
    }
    if (minutes === 60) {
        showNotification("Congratulations! You've reached 1 hour!", "success");
        resetTimeData();
    }
    displayTimer();
    updateProgressRings();
}

// Function to add timer pulse animation
function addTimerPulse() {
    const timeElements = [minuteLabel, secondLabel, millisecondLabel];
    timeElements.forEach(element => {
        if (element) {
            element.style.transform = 'scale(1.05)';
            setTimeout(() => {
                element.style.transform = 'scale(1)';
            }, 200);
        }
    });
}

// Function to add celebration effect
function addCelebrationEffect() {
    const svgContainer = document.querySelector('.svg-container');
    svgContainer.style.filter = 'hue-rotate(360deg)';
    setTimeout(() => {
        svgContainer.style.filter = 'none';
    }, 1000);
}

// Function to display the timer
function displayTimer() {
    millisecondLabel.innerHTML = padTime(milliseconds);
    secondLabel.innerHTML = padTime(seconds);
    minuteLabel.innerHTML = padTime(minutes);
}

// Function to pad time with leading zeros
function padTime(time) {
    return time.toString().padStart(2, '0');
}

// Function to reset time data
function resetTimeData() {
    milliseconds = 0;
    seconds = 0;
    minutes = 0;
}

// Function to add a lap to the lap list
function addToList() {
    const lapTime = `${padTime(minutes)}:${padTime(seconds)}:${padTime(milliseconds)}`;
    const listItem = document.createElement('li');
    listItem.classList.add('lap');
    listItem.style.opacity = '0';
    listItem.style.transform = 'translateX(-20px)';

    listItem.innerHTML = `
            <p>
                <strong>Lap ${lapList.childElementCount + 1}: </strong>
                <span>${lapTime}</span>
            </p>

            <button title="delete lap">
                <i class="fa-solid fa-trash"></i>
            </button>`;

    lapList.appendChild(listItem);

    // Animate the new lap item
    setTimeout(() => {
        listItem.style.transition = 'all 0.3s ease';
        listItem.style.opacity = '1';
        listItem.style.transform = 'translateX(0)';
    }, 10);

    listItem.scrollIntoView({behavior: 'smooth'});
}

// Function to update progress rings (visual enhancement)
function updateProgressRings() {
    const minuteRings = document.querySelectorAll('.clock-minutes path[id^="ring-"]');
    const secondRings = document.querySelectorAll('.clock-seconds path[id^="ring-"]');

    // Update minute rings
    const minuteProgress = (minutes / 60) * minuteRings.length;
    minuteRings.forEach((ring, index) => {
        if (index < minuteProgress) {
            ring.style.opacity = '1';
        } else {
            ring.style.opacity = '0.3';
        }
    });

    // Update second rings
    const secondProgress = (seconds / 60) * secondRings.length;
    secondRings.forEach((ring, index) => {
        if (index < secondProgress) {
            ring.style.opacity = '1';
        } else {
            ring.style.opacity = '0.3';
        }
    });
}

// Function to show notifications
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 2rem;
        background: var(--glass-bg);
        backdrop-filter: blur(15px);
        border: 1px solid var(--glass-border);
        border-radius: 10px;
        color: var(--text-primary);
        font-weight: 600;
        z-index: 1000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        box-shadow: var(--shadow-primary);
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 10);

    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Keyboard shortcuts
document.addEventListener('keydown', (event) => {
    if (event.code === 'Space') {
        event.preventDefault();
        if (!startButton.disabled) {
            startButton.click();
        } else if (!pauseButton.disabled) {
            pauseButton.click();
        }
    } else if (event.code === 'KeyR' && event.ctrlKey) {
        event.preventDefault();
        resetButton.click();
    } else if (event.code === 'KeyL' && event.ctrlKey) {
        event.preventDefault();
        if (!lapButton.disabled) {
            lapButton.click();
        }
    }
});

// Theme Toggle Functionality
const themeToggle = document.getElementById('theme-toggle');
const themeIcon = themeToggle.querySelector('i');

themeToggle.addEventListener('click', () => {
    document.body.classList.toggle('dark-theme');
    const isDark = document.body.classList.contains('dark-theme');

    themeIcon.className = isDark ? 'fa-solid fa-sun' : 'fa-solid fa-moon';

    // Save theme preference
    localStorage.setItem('theme', isDark ? 'dark' : 'light');

    playClickSound();
    addButtonAnimation(themeToggle);
});

// Load saved theme
const savedTheme = localStorage.getItem('theme');
if (savedTheme === 'dark') {
    document.body.classList.add('dark-theme');
    themeIcon.className = 'fa-solid fa-sun';
}

// Floating Particles
function createFloatingParticles() {
    const particlesContainer = document.getElementById('floating-particles');
    const particleCount = 50;

    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 15 + 's';
        particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
        particlesContainer.appendChild(particle);
    }
}

// Performance optimization: Create particles only if user prefers motion
if (!window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
    createFloatingParticles();
}

// Add welcome message
setTimeout(() => {
    showNotification('Welcome! Use Space to start/pause, Ctrl+R to reset, Ctrl+L for lap', 'info');
}, 1000);

// Initialize the display
displayTimer();
updateProgressRings();