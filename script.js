// Get DOM elements
const minuteLabel = document.getElementById('time-minutes');
const secondLabel = document.getElementById('time-seconds');
const millisecondLabel = document.getElementById('time-milliseconds');
const startButton = document.querySelector('#btn-start');
const pauseButton = document.querySelector('#btn-pause');
const resetButton = document.querySelector('#btn-reset');
const lapButton = document.querySelector('#btn-lap');
const deleteButton = document.querySelector('#btn-delete');
const lapList = document.querySelector('.lap-list');
const lapEmptyState = document.getElementById('lap-empty-state');
const lapCount = document.querySelector('.lap-count');
const bestLap = document.querySelector('.best-lap');

// Stopwatch variables
let minutes = 0;
let seconds = 0;
let milliseconds = 0;
let interval;
let lapTimes = [];
let bestLapTime = null;

// Sound effects (using Web Audio API for better performance)
const audioContext = new (window.AudioContext || window.webkitAudioContext)();

function playClickSound() {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);

    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.1);
}

function playStartSound() {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(800, audioContext.currentTime + 0.2);

    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.2);
}

function playStopSound() {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(200, audioContext.currentTime + 0.3);

    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.3);
}

// Disable all buttons except the Start button initially
toggleButtonState(true);

// Event listeners for the buttons

// Start button click event
startButton.addEventListener('click', () => {
    playStartSound();
    addButtonAnimation(startButton);
    interval = setInterval(updateTimer, 10);
    startButton.disabled = true;
    pauseButton.disabled = false;
    lapButton.disabled = false;
    toggleButtonState(false);
});

// Pause button click event
pauseButton.addEventListener('click', () => {
    playStopSound();
    addButtonAnimation(pauseButton);
    clearInterval(interval);
    pauseButton.disabled = true;
    startButton.disabled = false;
    lapButton.disabled = true;
});

// Reset button click event
resetButton.addEventListener('click', () => {
    playClickSound();
    addButtonAnimation(resetButton);
    clearInterval(interval);
    resetTimeData();
    displayTimer();
    updateProgressRings(); // Reset the progress rings
    pauseButton.disabled = true;
    startButton.disabled = false;
    lapButton.disabled = true;

    // Add reset animation to the clock
    const svgContainer = document.querySelector('.svg-container');
    svgContainer.style.transform = 'scale(0.95)';
    setTimeout(() => {
        svgContainer.style.transform = 'scale(1)';
    }, 150);

    showNotification('Timer reset', 'info');
});

// Lap button click event
lapButton.addEventListener('click', () => {
    playClickSound();
    addButtonAnimation(lapButton);
    addToList();
    deleteButton.disabled = false;
});

// Delete button click event
deleteButton.addEventListener('click', () => {
    playClickSound();
    addButtonAnimation(deleteButton);
    deleteSelectedLaps();
});

// Lap list click event to handle checkbox changes
lapList.addEventListener('change', function (event) {
    if (event.target.type === 'checkbox') {
        const lapItem = event.target.closest('.lap');
        if (lapItem) {
            if (event.target.checked) {
                lapItem.classList.add('selected');
            } else {
                lapItem.classList.remove('selected');
            }
            updateDeleteButtonState();
        }
    }
});

// Functions to handle the events

// Function to add button animation
function addButtonAnimation(button) {
    button.style.transform = 'scale(0.95)';
    setTimeout(() => {
        button.style.transform = 'scale(1)';
    }, 100);
}

// Function to toggle button states
function toggleButtonState(state) {
    pauseButton.disabled = state;
    resetButton.disabled = state;
    lapButton.disabled = state;
    deleteButton.disabled = state;
}

// Function to update the timer
function updateTimer() {
    milliseconds++;
    if (milliseconds === 100) {
        milliseconds = 0;
        seconds++;
        // Add subtle pulse animation every second
        if (seconds % 5 === 0) {
            addTimerPulse();
        }
    }
    if (seconds === 60) {
        seconds = 0;
        minutes++;
        // Add celebration effect every minute
        addCelebrationEffect();
    }
    if (minutes === 60) {
        showNotification("Congratulations! You've reached 1 hour!", "success");
        resetTimeData();
    }
    displayTimer();
    updateProgressRings();
}

// Function to add timer pulse animation (removed to prevent numbers flying off)
function addTimerPulse() {
    // Animation removed to prevent display issues
}

// Function to add celebration effect
function addCelebrationEffect() {
    const svgContainer = document.querySelector('.svg-container');
    svgContainer.style.filter = 'hue-rotate(360deg)';
    setTimeout(() => {
        svgContainer.style.filter = 'none';
    }, 1000);
}

// Function to display the timer
function displayTimer() {
    millisecondLabel.innerHTML = padTime(milliseconds);
    secondLabel.innerHTML = padTime(seconds);
    minuteLabel.innerHTML = padTime(minutes);
}

// Function to pad time with leading zeros
function padTime(time) {
    return time.toString().padStart(2, '0');
}

// Function to reset time data
function resetTimeData() {
    milliseconds = 0;
    seconds = 0;
    minutes = 0;
}

// Function to add a lap to the lap list
function addToList() {
    const currentTime = minutes * 60000 + seconds * 1000 + milliseconds * 10; // Convert to milliseconds
    const lapTimeString = `${padTime(minutes)}:${padTime(seconds)}:${padTime(milliseconds)}`;

    // Add to lap times array
    lapTimes.push({
        time: currentTime,
        timeString: lapTimeString,
        lapNumber: lapTimes.length + 1
    });

    // Update best lap time
    if (bestLapTime === null || currentTime < bestLapTime) {
        bestLapTime = currentTime;
    }

    // Hide empty state
    lapEmptyState.classList.add('hidden');

    // Create lap item
    const listItem = document.createElement('li');
    listItem.classList.add('lap');
    listItem.dataset.lapIndex = lapTimes.length - 1;
    listItem.style.opacity = '0';
    listItem.style.transform = 'translateX(-20px)';

    // Calculate difference from best lap
    let diffElement = '';
    if (lapTimes.length > 1) {
        const diff = currentTime - bestLapTime;
        if (diff === 0) {
            diffElement = '<div class="lap-diff faster">🏆 Best Lap</div>';
        } else {
            const diffSeconds = Math.floor(diff / 1000);
            const diffMs = Math.floor((diff % 1000) / 10);
            const diffString = `+${padTime(Math.floor(diffSeconds / 60))}:${padTime(diffSeconds % 60)}:${padTime(diffMs)}`;
            diffElement = `<div class="lap-diff slower">${diffString}</div>`;
        }
    } else {
        diffElement = '<div class="lap-diff">First Lap</div>';
    }

    listItem.innerHTML = `
        <div class="lap-info">
            <div class="lap-number">Lap ${lapTimes.length}</div>
            <div class="lap-time">${lapTimeString}</div>
            ${diffElement}
        </div>
        <label class="lap-checkbox">
            <input type="checkbox" data-lap-index="${lapTimes.length - 1}">
            <span class="checkmark"></span>
        </label>`;

    // Insert at the beginning of the list (most recent first)
    lapList.insertBefore(listItem, lapList.firstChild);

    // Animate the new lap item
    setTimeout(() => {
        listItem.style.transition = 'all 0.3s ease';
        listItem.style.opacity = '1';
        listItem.style.transform = 'translateX(0)';
    }, 10);

    updateLapStats();
    updateDeleteButtonState();
    listItem.scrollIntoView({behavior: 'smooth'});
}

// Function to update progress rings (visual enhancement)
function updateProgressRings() {
    const minuteRings = document.querySelectorAll('.clock-minutes path[id^="ring-"]');
    const secondRings = document.querySelectorAll('.clock-seconds path[id^="ring-"]');

    // Update minute rings
    const minuteProgress = (minutes / 60) * minuteRings.length;
    minuteRings.forEach((ring, index) => {
        if (index < minuteProgress) {
            ring.style.opacity = '1';
        } else {
            ring.style.opacity = '0.3';
        }
    });

    // Update second rings
    const secondProgress = (seconds / 60) * secondRings.length;
    secondRings.forEach((ring, index) => {
        if (index < secondProgress) {
            ring.style.opacity = '1';
        } else {
            ring.style.opacity = '0.3';
        }
    });
}

// Function to show notifications
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 2rem;
        background: var(--glass-bg);
        backdrop-filter: blur(15px);
        border: 1px solid var(--glass-border);
        border-radius: 10px;
        color: var(--text-primary);
        font-weight: 600;
        z-index: 1000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        box-shadow: var(--shadow-primary);
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 10);

    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Keyboard shortcuts
document.addEventListener('keydown', (event) => {
    if (event.code === 'Space') {
        event.preventDefault();
        if (!startButton.disabled) {
            startButton.click();
        } else if (!pauseButton.disabled) {
            pauseButton.click();
        }
    } else if (event.code === 'KeyR' && event.ctrlKey) {
        event.preventDefault();
        resetButton.click();
    } else if (event.code === 'KeyL' && event.ctrlKey) {
        event.preventDefault();
        if (!lapButton.disabled) {
            lapButton.click();
        }
    }
});

// Lap Management Functions
function updateLapStats() {
    lapCount.textContent = `${lapTimes.length} lap${lapTimes.length !== 1 ? 's' : ''}`;

    if (bestLapTime !== null) {
        const bestMinutes = Math.floor(bestLapTime / 60000);
        const bestSeconds = Math.floor((bestLapTime % 60000) / 1000);
        const bestMs = Math.floor((bestLapTime % 1000) / 10);
        bestLap.textContent = `Best: ${padTime(bestMinutes)}:${padTime(bestSeconds)}:${padTime(bestMs)}`;
    } else {
        bestLap.textContent = 'Best: --:--:--';
    }

    updateDeleteButtonState();
}

function clearAllLaps() {
    lapTimes = [];
    bestLapTime = null;
    lapList.innerHTML = '';
    lapEmptyState.classList.remove('hidden');
    updateLapStats();
    showNotification('All lap times cleared', 'info');
}

function deleteSelectedLaps() {
    const checkboxes = lapList.querySelectorAll('input[type="checkbox"]:checked');

    if (checkboxes.length === 0) {
        // If no laps are selected, delete all laps
        clearAllLaps();
        return;
    }

    // Get indices of selected laps
    const selectedIndices = Array.from(checkboxes).map(checkbox =>
        parseInt(checkbox.dataset.lapIndex)
    ).sort((a, b) => b - a); // Sort in descending order to remove from end first

    // Remove selected laps
    selectedIndices.forEach(index => {
        if (index >= 0 && index < lapTimes.length) {
            lapTimes.splice(index, 1);
        }
    });

    // Recalculate best lap time
    bestLapTime = null;
    if (lapTimes.length > 0) {
        bestLapTime = Math.min(...lapTimes.map(lap => lap.time));
    }

    // Re-render all laps
    renderAllLaps();
    updateLapStats();

    if (lapTimes.length === 0) {
        lapEmptyState.classList.remove('hidden');
    }

    const deletedCount = selectedIndices.length;
    showNotification(`${deletedCount} lap${deletedCount > 1 ? 's' : ''} deleted`, 'info');
}

function updateDeleteButtonState() {
    const checkboxes = lapList.querySelectorAll('input[type="checkbox"]');
    const checkedBoxes = lapList.querySelectorAll('input[type="checkbox"]:checked');

    if (checkboxes.length === 0) {
        deleteButton.disabled = true;
        deleteButton.innerHTML = '<i class="fa-solid fa-trash"></i><span>Delete</span>';
    } else if (checkedBoxes.length === 0) {
        deleteButton.disabled = false;
        deleteButton.innerHTML = '<i class="fa-solid fa-trash"></i><span>Delete All</span>';
    } else {
        deleteButton.disabled = false;
        deleteButton.innerHTML = `<i class="fa-solid fa-trash"></i><span>Delete (${checkedBoxes.length})</span>`;
    }
}

function removeLap(index) {
    if (index >= 0 && index < lapTimes.length) {
        lapTimes.splice(index, 1);

        // Recalculate best lap time
        bestLapTime = null;
        if (lapTimes.length > 0) {
            bestLapTime = Math.min(...lapTimes.map(lap => lap.time));
        }

        // Re-render all laps
        renderAllLaps();
        updateLapStats();

        if (lapTimes.length === 0) {
            lapEmptyState.classList.remove('hidden');
        }

        showNotification('Lap time removed', 'info');
    }
}

function renderAllLaps() {
    lapList.innerHTML = '';

    // Render laps in reverse order (most recent first)
    for (let i = lapTimes.length - 1; i >= 0; i--) {
        const lap = lapTimes[i];
        const listItem = document.createElement('li');
        listItem.classList.add('lap');
        listItem.dataset.lapIndex = i;

        // Calculate difference from best lap
        let diffElement = '';
        const diff = lap.time - bestLapTime;
        if (diff === 0) {
            diffElement = '<div class="lap-diff faster">🏆 Best Lap</div>';
        } else if (lapTimes.length > 1) {
            const diffSeconds = Math.floor(diff / 1000);
            const diffMs = Math.floor((diff % 1000) / 10);
            const diffString = `+${padTime(Math.floor(diffSeconds / 60))}:${padTime(diffSeconds % 60)}:${padTime(diffMs)}`;
            diffElement = `<div class="lap-diff slower">${diffString}</div>`;
        } else {
            diffElement = '<div class="lap-diff">First Lap</div>';
        }

        listItem.innerHTML = `
            <div class="lap-info">
                <div class="lap-number">Lap ${lap.lapNumber}</div>
                <div class="lap-time">${lap.timeString}</div>
                ${diffElement}
            </div>
            <label class="lap-checkbox">
                <input type="checkbox" data-lap-index="${i}">
                <span class="checkmark"></span>
            </label>`;

        lapList.appendChild(listItem);
    }
    updateDeleteButtonState();
}

// Floating Particles
function createFloatingParticles() {
    const particlesContainer = document.getElementById('floating-particles');
    const particleCount = 50;

    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 15 + 's';
        particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
        particlesContainer.appendChild(particle);
    }
}

// Performance optimization: Create particles only if user prefers motion
if (!window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
    createFloatingParticles();
}

// Add welcome message
setTimeout(() => {
    showNotification('Welcome! Use Space to start/pause, Ctrl+R to reset, Ctrl+L for lap', 'info');
}, 1000);

// Initialize the display
displayTimer();
updateProgressRings();
updateLapStats();